# Silicon Based Teahouse 2077 - 项目规划Tickets

## 概述

基于当前WeChat工作流修复完成状态，制定下一步优化和改进计划。

## Ticket #1: Admin管理界面统一入口 (高优先级)

### 问题描述
当前admin管理界面缺少统一的入口页面，用户需要直接访问具体路径。

### 当前状态
- `/admin/articles` - 文章列表管理
- `/admin/articles/new` - 新建文章
- `/admin/articles/[id]/edit` - 编辑文章
- 缺少 `/admin` 主页面

### 解决方案
1. **创建Admin主页面** (`/admin/page.tsx`)
   - 统一的管理界面入口
   - 导航到各个管理功能
   - 显示系统状态概览

2. **改进导航结构**
   - 添加面包屑导航
   - 统一的侧边栏或顶部导航
   - 保持cyberpunk主题一致性

### 实施步骤
- [ ] 创建 `src/app/admin/page.tsx`
- [ ] 设计管理界面布局组件
- [ ] 添加导航组件
- [ ] 更新现有管理页面的导航

### 预期效果
- 提供清晰的管理界面入口
- 改善用户体验
- 统一的界面风格

## Ticket #2: 目录结构优化 (中优先级)

### 问题描述
当前项目结构存在一些不一致和冗余。

### 优化建议

#### 2.1 组件目录重组
```
src/components/
├── admin/              # 管理界面专用组件
├── editor/             # 编辑器相关组件
├── layout/             # 布局组件
├── posts/              # 文章展示组件 (WeChat组件)
├── ui/                 # 基础UI组件 (shadcn/ui)
└── common/             # 通用组件
```

#### 2.2 路由组织改进
```
src/app/
├── admin/              # 管理界面
│   ├── page.tsx        # 管理主页
│   ├── articles/       # 文章管理
│   └── settings/       # 设置页面 (未来)
├── posts/              # 文章展示
├── api/                # API路由
└── tools/              # 工具页面
```

### 实施步骤
- [ ] 创建新的目录结构
- [ ] 逐步迁移现有组件
- [ ] 更新导入路径
- [ ] 测试所有功能

## Ticket #3: 遗留系统清理 (低优先级)

### 识别的遗留组件

#### 3.1 可能删除的文件
- 旧的Markdown处理组件
- 未使用的样式文件
- 过时的配置文件

#### 3.2 迁移/删除计划
1. **第一阶段：识别**
   - 分析代码依赖关系
   - 标记未使用的文件
   - 确认安全删除列表

2. **第二阶段：清理**
   - 删除确认无用的文件
   - 更新相关配置
   - 清理package.json依赖

### 实施步骤
- [ ] 运行依赖分析工具
- [ ] 创建删除清单
- [ ] 逐步删除并测试
- [ ] 更新文档

## Ticket #4: 单用户优化策略 (中优先级)

### 针对个人使用场景的优化

#### 4.1 功能简化
- 移除多用户相关功能
- 简化权限管理
- 优化单用户工作流

#### 4.2 性能优化重点
- 减少不必要的API调用
- 优化数据库查询
- 改进缓存策略

#### 4.3 维护便利性改进
- 简化部署流程
- 添加健康检查
- 改进错误处理和日志

### 实施步骤
- [ ] 分析当前多用户功能
- [ ] 设计简化方案
- [ ] 实施性能优化
- [ ] 添加监控和日志

## Ticket #5: Admin新增文章页面优化 (高优先级)

### 当前问题
- 布局不够现代化
- 缺少实时预览功能
- HTML粘贴转换功能缺失

### 优化方案

#### 5.1 样式优化
- 采用现代化管理界面设计
- 保持cyberpunk主题一致性
- 改进响应式布局

#### 5.2 布局重构
- 标题输入框置于页面顶部
- 左右分栏：左侧编辑器，右侧实时预览
- 优化编辑器和预览区域比例

#### 5.3 新功能实现
- HTML粘贴自动转换为WeChat组件
- 实时预览转换效果
- 智能组件映射

### 技术实现要点
- 扩展TipTap编辑器的粘贴处理
- 集成现有的content-converter
- 保持WeChat组件兼容性

### 实施步骤
- [ ] 设计新的页面布局
- [ ] 实现HTML粘贴转换功能
- [ ] 添加实时预览
- [ ] 优化样式和交互

## 优先级排序

### 立即执行 (本周)
1. **Ticket #5**: Admin新增文章页面优化
2. **Ticket #1**: Admin管理界面统一入口

### 短期执行 (下周)
3. **Ticket #2**: 目录结构优化
4. **Ticket #4**: 单用户优化策略

### 长期执行 (未来)
5. **Ticket #3**: 遗留系统清理

## 成功指标

### 用户体验指标
- 文章创建时间减少50%
- 管理界面导航更直观
- 错误率降低

### 技术指标
- 代码结构更清晰
- 构建时间优化
- 维护成本降低

### 功能指标
- HTML转换准确率>90%
- 实时预览响应时间<500ms
- 系统稳定性提升
